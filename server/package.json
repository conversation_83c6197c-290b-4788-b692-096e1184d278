{"name": "boatbook-server", "version": "1.0.0", "description": "BoatBook API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'No build step needed for server'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-cache": "^5.1.2", "axios": "^1.6.0", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["weather", "api", "noaa", "boating"], "author": "BoatBook", "license": "MIT"}