const express = require('express');
const axios = require('axios');
const router = express.Router();

// NOAA API base URLs
const NOAA_TIDES_API = 'https://api.tidesandcurrents.noaa.gov/api/prod/datagetter';
const NOAA_STATIONS_API = 'https://api.tidesandcurrents.noaa.gov/mdapi/prod/webapi';
const NOAA_WEATHER_API = 'https://api.weather.gov';

// Popular stations for fallback
const POPULAR_STATIONS = [
  { id: '9414290', name: 'San Francisco, CA', lat: 37.8063, lon: -122.4659, state: 'CA' },
  { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.2100, state: 'CA' },
  { id: '9414750', name: 'Alameda, CA', lat: 37.7717, lon: -122.3000, state: 'CA' },
  { id: '9414863', name: 'Richmond, CA', lat: 37.9300, lon: -122.4100, state: 'CA' },
  { id: '9415020', name: 'Point Reyes, CA', lat: 38.0050, lon: -122.9767, state: 'CA' },
  { id: '9413450', name: 'Monterey, CA', lat: 36.6050, lon: -121.8883, state: 'CA' },
  { id: '9410230', name: 'San Diego, CA', lat: 32.7142, lon: -117.1736, state: 'CA' },
  { id: '8418150', name: 'Boston, MA', lat: 42.3584, lon: -71.0498, state: 'MA' },
  { id: '8518750', name: 'The Battery, NY', lat: 40.7000, lon: -74.0150, state: 'NY' },
  { id: '8594900', name: 'Washington, DC', lat: 38.8744, lon: -77.0213, state: 'DC' },
  { id: '8638863', name: 'Sewells Point, VA', lat: 36.9467, lon: -76.3300, state: 'VA' },
  { id: '8665530', name: 'Charleston, SC', lat: 32.7806, lon: -79.9256, state: 'SC' },
  { id: '8723970', name: 'Miami Beach, FL', lat: 25.7686, lon: -80.1300, state: 'FL' },
  { id: '8771450', name: 'Galveston, TX', lat: 29.3100, lon: -94.7900, state: 'TX' },
  { id: '9431647', name: 'Neah Bay, WA', lat: 48.3683, lon: -124.6017, state: 'WA' },
  { id: '9447130', name: 'Seattle, WA', lat: 47.6026, lon: -122.3393, state: 'WA' },
];

// Helper function to make NOAA API requests with proper headers
const fetchNOAAData = async (url, retries = 2) => {
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'BoatBook/1.0 (Weather Station App)',
          'Accept': 'application/json'
        },
        timeout: 10000 // 10 second timeout
      });

      // Check for NOAA API error responses
      if (response.data.error) {
        throw new Error(`NOAA API Error: ${response.data.error.message || 'Unknown error'}`);
      }

      return response.data;
    } catch (error) {
      console.warn(`NOAA API attempt ${attempt + 1} failed:`, error.message);
      
      if (attempt === retries) {
        throw error;
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

// Validate and parse numeric values
const validateAndParseValue = (value, fieldName) => {
  if (value === null || value === undefined || value === '') {
    return null;
  }
  
  const parsed = parseFloat(value);
  if (isNaN(parsed)) {
    console.warn(`Invalid ${fieldName} value: ${value}`);
    return null;
  }
  
  return parsed;
};

// Generate mock weather data for testing
const generateMockWeatherData = (stationId) => {
  const now = new Date();
  const baseTemp = stationId.startsWith('94') ? 15 : 20; // Cooler for SF Bay area
  const tempVariation = Math.sin(now.getHours() / 24 * 2 * Math.PI) * 5;
  
  return {
    timestamp: now.toISOString(),
    temperature: baseTemp + tempVariation + (Math.random() - 0.5) * 2,
    waterTemperature: baseTemp - 2 + (Math.random() - 0.5) * 1,
    windSpeed: 5 + Math.random() * 15,
    windGust: 8 + Math.random() * 20,
    windDirection: Math.random() * 360,
    tideLevel: Math.sin(now.getTime() / (1000 * 60 * 60 * 6)) * 2, // 6-hour tide cycle
    lightning: false,
  };
};

// GET /api/weather/data/:stationId - Get current weather data for a station
router.get('/data/:stationId', async (req, res) => {
  const { stationId } = req.params;
  const cacheKey = `weather-data-${stationId}`;

  try {
    // Check cache first
    const cached = req.cache.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for weather data: ${stationId}`);
      return res.json(cached);
    }

    console.log(`Fetching weather data for station: ${stationId}`);

    // Initialize default values
    let temperature = null;
    let waterTemperature = null;
    let windSpeed = null;
    let windDirection = null;
    let windGust = null;
    let tideLevel = null;
    let timestamp = new Date().toISOString();
    let hasAnyData = false;

    // Fetch tide data
    try {
      const tideData = await fetchNOAAData(
        `${NOAA_TIDES_API}?station=${stationId}&date=latest&time_zone=lst_ldt&datum=MLLW&units=metric&format=json&product=water_level`
      );

      if (tideData?.data?.[0]) {
        const value = validateAndParseValue(tideData.data[0].v, 'tide level');
        if (value !== null) {
          tideLevel = value;
          hasAnyData = true;
          timestamp = new Date(tideData.data[0].t).toISOString();
        }
      }
    } catch (error) {
      console.warn('Error fetching tide data:', error.message);
    }

    // Fetch air temperature
    try {
      const airTempData = await fetchNOAAData(
        `${NOAA_TIDES_API}?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=air_temperature`
      );

      if (airTempData?.data?.[0]) {
        const value = validateAndParseValue(airTempData.data[0].v, 'air temperature');
        if (value !== null) {
          temperature = value;
          hasAnyData = true;
        }
      }
    } catch (error) {
      console.warn('Error fetching air temperature:', error.message);
    }

    // Fetch water temperature
    try {
      const waterTempData = await fetchNOAAData(
        `${NOAA_TIDES_API}?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=water_temperature`
      );

      if (waterTempData?.data?.[0]) {
        const value = validateAndParseValue(waterTempData.data[0].v, 'water temperature');
        if (value !== null) {
          waterTemperature = value;
          hasAnyData = true;
        }
      }
    } catch (error) {
      console.warn('Error fetching water temperature:', error.message);
    }

    // Fetch wind data
    try {
      const windData = await fetchNOAAData(
        `${NOAA_TIDES_API}?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=wind`
      );

      if (windData?.data?.[0]) {
        const speed = validateAndParseValue(windData.data[0].s, 'wind speed');
        const direction = validateAndParseValue(windData.data[0].d, 'wind direction');
        const gust = validateAndParseValue(windData.data[0].g, 'wind gust');

        if (speed !== null) {
          windSpeed = speed;
          hasAnyData = true;
        }
        if (direction !== null) {
          windDirection = direction;
        }
        if (gust !== null) {
          windGust = gust;
        }
      }
    } catch (error) {
      console.warn('Error fetching wind data:', error.message);
    }

    let weatherData;

    if (!hasAnyData) {
      console.warn(`No weather data available for station ${stationId}, using mock data`);
      weatherData = generateMockWeatherData(stationId);
    } else {
      weatherData = {
        timestamp,
        temperature: temperature ?? 0,
        waterTemperature: waterTemperature ?? undefined,
        windSpeed: windSpeed ?? 0,
        windGust: windGust ?? 0,
        windDirection: windDirection ?? 0,
        tideLevel: tideLevel ?? undefined,
        lightning: false,
      };
    }

    // Cache the result
    req.cache.set(cacheKey, weatherData);

    res.json(weatherData);
  } catch (error) {
    console.error(`Error fetching weather data for station ${stationId}:`, error);

    // Return mock data as fallback
    const mockData = generateMockWeatherData(stationId);
    res.json(mockData);
  }
});

// GET /api/weather/stations/search?q=query - Search weather stations
router.get('/stations/search', async (req, res) => {
  const { q: query } = req.query;
  const cacheKey = `stations-search-${query}`;

  try {
    if (!query || query.length < 2) {
      return res.json([]);
    }

    // Check cache first
    const cached = req.cache.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for station search: ${query}`);
      return res.json(cached);
    }

    console.log(`Searching for weather stations with query: ${query}`);

    // First, search popular stations for quick results
    const popularResults = POPULAR_STATIONS.filter(station =>
      station.name.toLowerCase().includes(query.toLowerCase()) ||
      (station.state && station.state.toLowerCase().includes(query.toLowerCase()))
    );

    try {
      // Search NOAA's station metadata API
      const data = await fetchNOAAData(`${NOAA_STATIONS_API}/stations.json?type=tidepredictions`);

      const stations = [];

      if (data?.stations) {
        // Filter stations based on query
        const filteredStations = data.stations.filter((station) => {
          const name = station.name?.toLowerCase() || '';
          const state = station.state?.toLowerCase() || '';
          const queryLower = query.toLowerCase();

          return name.includes(queryLower) ||
                 state.includes(queryLower) ||
                 station.id?.includes(query);
        });

        // Convert to our WeatherStation format
        for (const station of filteredStations.slice(0, 20)) {
          if (station.lat && station.lng && station.name && station.id) {
            stations.push({
              id: station.id,
              name: station.name,
              lat: parseFloat(station.lat),
              lon: parseFloat(station.lng),
              state: station.state || undefined
            });
          }
        }
      }

      // Combine popular results with API results, removing duplicates
      const allResults = [...popularResults];
      const existingIds = new Set(popularResults.map(s => s.id));

      for (const station of stations) {
        if (!existingIds.has(station.id)) {
          allResults.push(station);
        }
      }

      const results = allResults.slice(0, 15);

      // Cache the results
      req.cache.set(cacheKey, results);

      res.json(results);
    } catch (apiError) {
      console.warn('NOAA station API not available, falling back to popular stations:', apiError.message);

      // Cache popular results
      req.cache.set(cacheKey, popularResults);

      res.json(popularResults);
    }
  } catch (error) {
    console.error('Error searching stations:', error);
    res.status(500).json({ error: 'Failed to search stations' });
  }
});

// GET /api/weather/stations/nearby?lat=x&lon=y&radius=z - Find nearby weather stations
router.get('/stations/nearby', async (req, res) => {
  const { lat, lon, radius = 100 } = req.query;
  const cacheKey = `stations-nearby-${lat}-${lon}-${radius}`;

  try {
    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const latitude = parseFloat(lat);
    const longitude = parseFloat(lon);
    const radiusKm = parseFloat(radius);

    if (isNaN(latitude) || isNaN(longitude) || isNaN(radiusKm)) {
      return res.status(400).json({ error: 'Invalid coordinates or radius' });
    }

    // Check cache first
    const cached = req.cache.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for nearby stations: ${lat}, ${lon}`);
      return res.json(cached);
    }

    console.log(`Finding stations near ${lat}, ${lon} within ${radiusKm}km`);

    // Calculate distance between two points using Haversine formula
    const calculateDistance = (lat1, lon1, lat2, lon2) => {
      const R = 6371; // Earth's radius in kilometers
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    };

    // First, check popular stations for nearby ones
    const nearbyPopular = POPULAR_STATIONS
      .map(station => ({
        ...station,
        distance: Math.round(calculateDistance(latitude, longitude, station.lat, station.lon) * 10) / 10
      }))
      .filter(station => station.distance <= radiusKm)
      .sort((a, b) => a.distance - b.distance);

    try {
      // Get all stations from NOAA API
      const data = await fetchNOAAData(`${NOAA_STATIONS_API}/stations.json?type=tidepredictions`);

      const allNearbyStations = [...nearbyPopular];
      const existingIds = new Set(nearbyPopular.map(s => s.id));

      if (data?.stations) {
        // Find nearby stations from the full list
        for (const station of data.stations) {
          if (station.lat && station.lng && station.name && station.id && !existingIds.has(station.id)) {
            const distance = calculateDistance(latitude, longitude, parseFloat(station.lat), parseFloat(station.lng));

            if (distance <= radiusKm) {
              allNearbyStations.push({
                id: station.id,
                name: station.name,
                lat: parseFloat(station.lat),
                lon: parseFloat(station.lng),
                state: station.state || undefined,
                distance: Math.round(distance * 10) / 10
              });
            }
          }
        }
      }

      // Sort by distance and return top results
      const results = allNearbyStations
        .sort((a, b) => (a.distance || 0) - (b.distance || 0))
        .slice(0, 15);

      // Cache the results
      req.cache.set(cacheKey, results);

      res.json(results);
    } catch (apiError) {
      console.warn('NOAA station API not available, using popular stations only:', apiError.message);

      // Cache popular results
      req.cache.set(cacheKey, nearbyPopular.slice(0, 10));

      res.json(nearbyPopular.slice(0, 10));
    }
  } catch (error) {
    console.error('Error finding nearby stations:', error);
    res.status(500).json({ error: 'Failed to find nearby stations' });
  }
});

// GET /api/weather/forecast/:stationId - Get hourly forecast for a station
router.get('/forecast/:stationId', async (req, res) => {
  const { stationId } = req.params;
  const cacheKey = `weather-forecast-${stationId}`;

  try {
    // Check cache first (longer cache for forecasts - 30 minutes)
    const cached = req.cache.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for weather forecast: ${stationId}`);
      return res.json(cached);
    }

    console.log(`Fetching weather forecast for station: ${stationId}`);

    // For now, return a simple forecast based on current conditions
    // In a full implementation, you'd integrate with NWS forecast APIs
    const hours = [];
    const now = new Date();

    for (let i = 0; i < 24; i++) {
      const forecastTime = new Date(now.getTime() + i * 60 * 60 * 1000);
      const hour = forecastTime.getHours();

      // Generate realistic forecast data
      const baseTemp = stationId.startsWith('94') ? 15 : 20;
      const tempVariation = Math.sin(hour / 24 * 2 * Math.PI) * 5;
      const windVariation = Math.sin((hour + 6) / 24 * 2 * Math.PI) * 5 + 5;

      hours.push({
        timestamp: forecastTime.toISOString(),
        temperature: baseTemp + tempVariation + (Math.random() - 0.5) * 2,
        waterTemperature: baseTemp - 2,
        windSpeed: 5 + windVariation + Math.random() * 5,
        windGust: 8 + windVariation + Math.random() * 8,
        windDirection: 180 + Math.sin(hour / 12 * Math.PI) * 90,
        tideLevel: Math.sin((hour * 60) / (6 * 60) * 2 * Math.PI) * 2,
        lightning: false,
      });
    }

    const forecast = { hours };

    // Cache for 30 minutes
    req.cache.set(cacheKey, forecast, 1800);

    res.json(forecast);
  } catch (error) {
    console.error(`Error fetching forecast for station ${stationId}:`, error);
    res.status(500).json({ error: 'Failed to fetch forecast' });
  }
});

// GET /api/weather/alerts?lat=x&lon=y - Get weather alerts for coordinates
router.get('/alerts', async (req, res) => {
  const { lat, lon } = req.query;
  const cacheKey = `weather-alerts-${lat}-${lon}`;

  try {
    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    // Check cache first
    const cached = req.cache.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for weather alerts: ${lat}, ${lon}`);
      return res.json(cached);
    }

    console.log(`Fetching weather alerts for coordinates: ${lat}, ${lon}`);

    try {
      // Use NOAA Weather Service API for alerts
      const alertsData = await fetchNOAAData(`${NOAA_WEATHER_API}/alerts/active?point=${lat},${lon}`);

      const alerts = [];

      if (alertsData?.features) {
        for (const feature of alertsData.features) {
          const properties = feature.properties;

          // Filter for marine and weather-related alerts
          const marineEvents = ['marine', 'coastal', 'flood', 'wind', 'storm', 'hurricane', 'gale'];
          const isRelevant = marineEvents.some(event =>
            properties.event?.toLowerCase().includes(event) ||
            properties.headline?.toLowerCase().includes(event)
          );

          if (isRelevant && properties.id) {
            alerts.push({
              id: properties.id,
              title: properties.headline || properties.event || 'Weather Alert',
              description: properties.description || '',
              severity: properties.severity?.toLowerCase() || 'moderate',
              urgency: properties.urgency?.toLowerCase() || 'expected',
              certainty: properties.certainty?.toLowerCase() || 'likely',
              event: properties.event || '',
              effective: properties.effective || new Date().toISOString(),
              expires: properties.expires || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              areas: properties.areaDesc ? [properties.areaDesc] : []
            });
          }
        }
      }

      const result = {
        alerts,
        lastUpdated: new Date().toISOString()
      };

      // Cache for 15 minutes
      req.cache.set(cacheKey, result, 900);

      res.json(result);
    } catch (apiError) {
      console.warn('Weather alerts API not available:', apiError.message);

      // Return empty alerts as fallback
      const result = {
        alerts: [],
        lastUpdated: new Date().toISOString()
      };

      req.cache.set(cacheKey, result, 900);
      res.json(result);
    }
  } catch (error) {
    console.error('Error fetching weather alerts:', error);
    res.status(500).json({ error: 'Failed to fetch weather alerts' });
  }
});

module.exports = router;
