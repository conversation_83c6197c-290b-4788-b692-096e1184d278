
import { useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import { Toaster } from './components/ui/toaster';
import { Toaster as Sonner } from './components/ui/sonner';

import { Layout } from './components/Layout';
import Index from './pages/Index';
import Login from './pages/Login';
import Teams from './pages/Teams';
import Jaunts from './pages/Jaunts';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import Admin from './pages/Admin';
import Config from './pages/Config';
import Maintenance from './pages/Maintenance';
import NotFound from './pages/NotFound';
import Lineups from './pages/Lineups';

import './App.css';

function App() {
  const { user, loading } = useAuth();
  const location = useLocation();

  // Log page views for analytics
  useEffect(() => {
    console.log(`Page view: ${location.pathname}`);
  }, [location]);

  // Simple splash/loading screen
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="animate-pulse text-primary font-semibold text-xl">
          Loading...
        </div>
      </div>
    );
  }

  return (
    <>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/" element={<Layout />}>
          <Route index element={<Index />} />
          <Route path="teams" element={<Teams />} />
          <Route path="lineups" element={<Lineups />} />
          <Route path="jaunts" element={<Jaunts />} />
          <Route path="maintenance" element={<Maintenance />} />
          <Route path="profile" element={<Profile />} />
          <Route path="settings" element={<Settings />} />
          <Route path="admin" element={<Admin />} />
          <Route path="config" element={<Config />} />
          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>
      <Toaster />
      <Sonner position="top-right" />
    </>
  );
}

export default App;
