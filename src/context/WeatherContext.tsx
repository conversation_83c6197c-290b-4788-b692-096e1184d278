
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { WeatherStation, WeatherData, HourlyForecast, WeatherUnit } from '@/types/weather';
import { getWeatherData, getHourlyForecast, DEFAULT_STATION } from '@/services/weather.service';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';

interface WeatherContextType {
  currentStation: WeatherStation | null;
  setCurrentStation: (station: WeatherStation | null) => void;
  weatherData: WeatherData | null;
  hourlyForecast: HourlyForecast | null;
  isLoading: boolean;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  weatherUnit: WeatherUnit;
  setWeatherUnit: (unit: WeatherUnit) => void;
  error: Error | null;
  refreshWeather: () => void;
}

const WeatherContext = createContext<WeatherContextType | undefined>(undefined);

export const WeatherProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [currentStation, setCurrentStation] = useState<WeatherStation | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [weatherUnit, setWeatherUnit] = useState<WeatherUnit>('metric');
  
  // Load user's preferred station and unit from user data
  useEffect(() => {
    if (user?.preferredWeatherStation) {
      setCurrentStation(user.preferredWeatherStation);
    } else {
      setCurrentStation(DEFAULT_STATION);
    }
    
    if (user?.preferredWeatherUnit) {
      setWeatherUnit(user.preferredWeatherUnit as WeatherUnit);
    }
  }, [user]);
  
  // Query for weather data
  const { 
    data: weatherData, 
    isLoading: isLoadingWeather, 
    error: weatherError,
    refetch: refetchWeather
  } = useQuery({
    queryKey: ['weatherData', currentStation?.id],
    queryFn: () => currentStation ? getWeatherData(currentStation.id) : null,
    enabled: !!currentStation,
    refetchInterval: 1000 * 60 * 10, // Refetch every 10 minutes
    staleTime: 1000 * 60 * 5, // Data is stale after 5 minutes
    retry: 2, // Retry twice before giving up
  });
  
  // Query for hourly forecast
  const {
    data: hourlyForecast,
    isLoading: isLoadingForecast,
    error: forecastError,
    refetch: refetchForecast
  } = useQuery({
    queryKey: ['hourlyForecast', currentStation?.id],
    queryFn: () => currentStation ? getHourlyForecast(currentStation.id) : null,
    enabled: !!currentStation && isExpanded,
    refetchInterval: 1000 * 60 * 30, // Refetch every 30 minutes
    staleTime: 1000 * 60 * 15, // Data is stale after 15 minutes
    retry: 2, // Retry twice before giving up
  });
  
  // Refresh both weather data and hourly forecast
  const refreshWeather = () => {
    refetchWeather();
    if (isExpanded) {
      refetchForecast();
    }
  };
  
  // Show error toast if there's an error
  useEffect(() => {
    if (weatherError) {
      toast.error(t('weather_error'));
      console.error('Weather data error:', weatherError);
    }
    
    if (forecastError) {
      toast.error(t('weather_error'));
      console.error('Forecast data error:', forecastError);
    }
  }, [weatherError, forecastError, t]);
  
  const value = {
    currentStation,
    setCurrentStation,
    weatherData: weatherData || null,
    hourlyForecast: hourlyForecast || null,
    isLoading: isLoadingWeather || isLoadingForecast,
    isExpanded,
    setIsExpanded,
    weatherUnit,
    setWeatherUnit,
    error: weatherError || forecastError || null,
    refreshWeather,
  };
  
  return (
    <WeatherContext.Provider value={value}>
      {children}
    </WeatherContext.Provider>
  );
};

export const useWeather = (): WeatherContextType => {
  const context = useContext(WeatherContext);
  if (context === undefined) {
    throw new Error('useWeather must be used within a WeatherProvider');
  }
  return context;
};
