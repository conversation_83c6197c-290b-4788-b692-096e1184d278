
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent 
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { StationSearchPopover } from './weather/StationSearchPopover';
import { UnitSelectionGroup } from './weather/UnitSelectionGroup';
import { useWeatherSettings } from './weather/useWeatherSettings';

export const WeatherSettingsCard: React.FC = () => {
  const { t } = useLanguage();
  const {
    currentStation,
    weatherUnit,
    isStationOpen,
    setIsStationOpen,
    handleSelectStation,
    handleUnitChange
  } = useWeatherSettings();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('weather_title')}</CardTitle>
        <CardDescription>
          {t('weather_station_setting_description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="weatherStation">{t('weather_station_setting')}</Label>
          <StationSearchPopover
            currentStation={currentStation}
            onSelectStation={handleSelectStation}
            isOpen={isStationOpen}
            setIsOpen={setIsStationOpen}
          />
        </div>

        <UnitSelectionGroup
          currentUnit={weatherUnit}
          onUnitChange={handleUnitChange}
        />
      </CardContent>
    </Card>
  );
};
