import { ReactNode, useEffect } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useLanguage } from "@/context/LanguageContext";
import { NavigationHeader } from "./navigation";
import { WeatherBar } from "./weather/WeatherBar";
import { toast } from "sonner";

interface LayoutProps {
  children?: ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const { user, isAuthenticated, loading } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, loading, navigate]);

  useEffect(() => {
    if (isAuthenticated && user) {
      const welcomeMessage = `${t("welcome")}, ${user.name}!`;
      toast(welcomeMessage, {
        duration: 4000,
      });
    }
  }, [isAuthenticated, user, t]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-xl font-medium">{t("loading")}</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <NavigationHeader />
      <WeatherBar />
      <main className="flex-1 container mx-auto px-2 sm:px-4 py-4 sm:py-6">
        {children || <Outlet />}
      </main>
    </div>
  );
}
