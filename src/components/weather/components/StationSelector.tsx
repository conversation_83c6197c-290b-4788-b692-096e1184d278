
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { MapPin } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { useIsMobile } from '@/hooks/use-mobile';
import { WeatherStation } from '@/types/weather';
import { searchWeatherStations } from '@/services/weather.service';

interface StationSelectorProps {
  currentStation: WeatherStation | null;
  onSelectStation: (station: WeatherStation) => void;
}

export const StationSelector: React.FC<StationSelectorProps> = ({
  currentStation,
  onSelectStation,
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();
  const [query, setQuery] = React.useState('');
  const [searchResults, setSearchResults] = React.useState<WeatherStation[]>([]);
  const [isOpen, setIsOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  
  // Handle station search
  React.useEffect(() => {
    if (query.length > 1) {
      setLoading(true);
      
      const performSearch = async () => {
        try {
          const results = await searchWeatherStations(query);
          setSearchResults(results);
        } catch (error) {
          console.error('Error searching stations:', error);
          setSearchResults([]);
        } finally {
          setLoading(false);
        }
      };
      
      const timer = setTimeout(performSearch, 300);
      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
    }
  }, [query]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size={isMobile ? "default" : "sm"}
          className={`${isMobile ? 'h-10 w-[150px] text-sm' : 'h-7'} flex items-center`}
        >
          <MapPin className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'} mr-1 flex-shrink-0`} />
          <span className="truncate">
            {currentStation?.name || t('weather_station')}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[250px] lg:w-[300px]" align={isMobile ? "start" : "center"}>
        <Command>
          <CommandInput 
            placeholder={t('weather_search_placeholder')} 
            value={query}
            onValueChange={setQuery}
            className="h-9"
          />
          <CommandEmpty>
            {loading ? t('weather_loading') : t('weather_no_results')}
          </CommandEmpty>
          <CommandList>
            <CommandGroup>
              {searchResults.map((station) => (
                <CommandItem
                  key={station.id}
                  onSelect={() => {
                    onSelectStation(station);
                    setIsOpen(false);
                  }}
                >
                  <MapPin className="h-4 w-4 mr-2 opacity-50" />
                  <span>{station.name}</span>
                  {station.state && <span className="ml-1 text-muted-foreground">({station.state})</span>}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
