
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { WeatherStation, WeatherData, HourlyForecast as HourlyForecastType, WeatherUnit } from '@/types/weather';
import { formatTemperature, formatTime, formatWindDirection, formatTideLevel } from '@/utils/weatherUtils';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Droplet,
  ThermometerSun,
  Wind,
  ArrowUp,
  CloudLightning
} from 'lucide-react';

interface HourlyForecastProps {
  currentStation: WeatherStation | null;
  hourlyForecast: HourlyForecastType | null;
  weatherData: WeatherData | null;
  weatherUnit: WeatherUnit;
  isLoading: boolean;
}

export const HourlyForecast: React.FC<HourlyForecastProps> = ({
  currentStation,
  hourlyForecast,
  weatherData,
  weatherUnit,
  isLoading
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();
  
  if (!currentStation) {
    return (
      <div className="container mx-auto py-4 text-center">
        {t('weather_no_station_selected')}
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="container mx-auto py-4">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2">
          {Array(8).fill(0).map((_, index) => (
            <Card key={index} className="h-28">
              <CardContent className="p-2 flex flex-col items-center justify-center h-full">
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-4 w-12 mb-1" />
                <Skeleton className="h-4 w-10" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }
  
  if (!hourlyForecast || hourlyForecast.hours.length === 0) {
    return (
      <div className="container mx-auto py-4 text-center">
        {t('weather_forecast_not_available')}
      </div>
    );
  }
  
  // Only show next 8 hours on desktop, 4 on mobile
  const displayHours = isMobile 
    ? hourlyForecast.hours.slice(0, 4) 
    : hourlyForecast.hours.slice(0, 8);
  
  return (
    <div className="container mx-auto py-4">
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2">
        {displayHours.map((hour, index) => (
          <Card key={index} className="hover:bg-accent transition-colors">
            <CardContent className="p-2">
              <div className="text-center font-medium mb-1">
                {formatTime(hour.timestamp)}
              </div>
              
              <div className="grid grid-cols-2 gap-x-1 gap-y-0.5 text-xs">
                {/* Air Temperature */}
                <div className="flex items-center" title={t('weather_temperature')}>
                  <ThermometerSun className="h-3 w-3 mr-1 text-orange-500" />
                  <span>{formatTemperature(hour.temperature, weatherUnit)}</span>
                </div>
                
                {/* Water Temperature - show if available */}
                {hour.waterTemperature !== undefined && (
                  <div className="flex items-center" title={t('weather_water_temp')}>
                    <Droplet className="h-3 w-3 mr-1 text-blue-500" />
                    <span>
                      {hour.waterTemperature !== null 
                        ? formatTemperature(hour.waterTemperature, weatherUnit)
                        : t('weather_not_available')}
                    </span>
                  </div>
                )}
                
                {/* Wind */}
                <div className="flex items-center" title={t('weather_wind_speed')}>
                  <Wind className="h-3 w-3 mr-1 text-teal-500" />
                  <span>{Math.round(hour.windSpeed)}</span>
                </div>
                
                {/* Wind Direction */}
                <div className="flex items-center" title={t('weather_wind_direction')}>
                  <ArrowUp 
                    className="h-3 w-3 mr-1 text-sky-500" 
                    style={{ transform: `rotate(${hour.windDirection}deg)` }}
                  />
                  <span>{formatWindDirection(hour.windDirection)}</span>
                </div>
                
                {/* Tide Level - only show if available */}
                {hour.tideLevel !== undefined && hour.tideLevel !== null && (
                  <div className="col-span-2 flex items-center justify-center mt-1">
                    <span className="text-center" title={t('weather_tide_level')}>
                      {t('weather_tide')}: {formatTideLevel(hour.tideLevel, weatherUnit)}
                    </span>
                  </div>
                )}
                
                {/* Lightning Warning - only show if true */}
                {hour.lightning && (
                  <div className="col-span-2 flex items-center justify-center text-red-500 mt-1">
                    <CloudLightning className="h-3 w-3 mr-1" />
                    <span>{t('weather_lightning_warning')}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
