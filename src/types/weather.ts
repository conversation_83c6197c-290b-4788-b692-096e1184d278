
export interface WeatherStation {
  id: string;
  name: string;
  lat: number;
  lon: number;
  state?: string;
  distance?: number;
}

export interface WeatherData {
  timestamp: string;
  temperature: number;
  waterTemperature?: number;
  windSpeed: number;
  windGust: number;
  windDirection: number;
  tideLevel?: number;
  lightning: boolean;
}

export interface HourlyForecast {
  hours: WeatherData[];
}

export type WeatherUnit = 'metric' | 'imperial';
