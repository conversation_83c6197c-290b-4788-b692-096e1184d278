
import { WeatherStation, WeatherData, HourlyForecast, WeatherAlerts, WeatherAlert, WeatherStationCapabilities } from '@/types/weather';

// Default station for San Francisco Bay
export const DEFAULT_STATION: WeatherStation = {
  id: '9414290',
  name: 'San Francisco, CA',
  lat: 37.8063,
  lon: -122.4659,
  state: 'CA'
};

// List of popular NOAA stations for quick access
export const POPULAR_STATIONS: WeatherStation[] = [
  { id: '9414290', name: 'San Francisco, CA', lat: 37.8063, lon: -122.4659, state: 'CA' },
  { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.2100, state: 'CA' },
  { id: '9414750', name: 'Alameda, CA', lat: 37.7717, lon: -122.3000, state: 'CA' },
  { id: '9414863', name: 'Richmond, CA', lat: 37.9300, lon: -122.4100, state: 'CA' },
  { id: '9415020', name: 'Point Reyes, CA', lat: 38.0050, lon: -122.9767, state: 'CA' },
  { id: '9413450', name: 'Monterey, CA', lat: 36.6050, lon: -121.8883, state: 'CA' },
  { id: '9410230', name: 'San Diego, CA', lat: 32.7142, lon: -117.1736, state: 'CA' },
  { id: '8418150', name: 'Boston, MA', lat: 42.3584, lon: -71.0498, state: 'MA' },
  { id: '8518750', name: 'The Battery, NY', lat: 40.7000, lon: -74.0150, state: 'NY' },
  { id: '8594900', name: 'Washington, DC', lat: 38.8744, lon: -77.0213, state: 'DC' },
  { id: '8638863', name: 'Sewells Point, VA', lat: 36.9467, lon: -76.3300, state: 'VA' },
  { id: '8665530', name: 'Charleston, SC', lat: 32.7806, lon: -79.9256, state: 'SC' },
  { id: '8723970', name: 'Miami Beach, FL', lat: 25.7686, lon: -80.1300, state: 'FL' },
  { id: '8771450', name: 'Galveston, TX', lat: 29.3100, lon: -94.7900, state: 'TX' },
  { id: '9431647', name: 'Neah Bay, WA', lat: 48.3683, lon: -124.6017, state: 'WA' },
  { id: '9447130', name: 'Seattle, WA', lat: 47.6026, lon: -122.3393, state: 'WA' },
];

/**
 * Search weather stations by name using NOAA station metadata API
 */
export const searchWeatherStations = async (query: string): Promise<WeatherStation[]> => {
  try {
    console.log(`Searching for weather stations with query: ${query}`);

    // First, search popular stations for quick results
    const popularResults = POPULAR_STATIONS.filter(station =>
      station.name.toLowerCase().includes(query.toLowerCase()) ||
      (station.state && station.state.toLowerCase().includes(query.toLowerCase()))
    );

    // If we have good popular results, return them quickly
    if (popularResults.length > 0) {
      return popularResults.slice(0, 10);
    }

    // Otherwise, search NOAA's station metadata API
    try {
      const data = await fetchNOAAData(
        `https://api.tidesandcurrents.noaa.gov/mdapi/prod/webapi/stations.json?type=tidepredictions`
      );
      const stations: WeatherStation[] = [];

      if (data?.stations) {
        // Filter stations based on query
        const filteredStations = data.stations.filter((station: any) => {
          const name = station.name?.toLowerCase() || '';
          const state = station.state?.toLowerCase() || '';
          const queryLower = query.toLowerCase();

          return name.includes(queryLower) ||
                 state.includes(queryLower) ||
                 station.id?.includes(query);
        });

        // Convert to our WeatherStation format
        for (const station of filteredStations.slice(0, 20)) {
          if (station.lat && station.lng && station.name && station.id) {
            stations.push({
              id: station.id,
              name: station.name,
              lat: parseFloat(station.lat),
              lon: parseFloat(station.lng),
              state: station.state || undefined
            });
          }
        }
      }

      // Combine popular results with API results, removing duplicates
      const allResults = [...popularResults];
      const existingIds = new Set(popularResults.map(s => s.id));

      for (const station of stations) {
        if (!existingIds.has(station.id)) {
          allResults.push(station);
        }
      }

      return allResults.slice(0, 15);
    } catch (apiError) {
      console.warn('NOAA station API not available, falling back to popular stations:', apiError);
      return popularResults;
    }
  } catch (error) {
    console.error('Error searching weather stations:', error);
    // Fallback to popular stations search
    return POPULAR_STATIONS.filter(station =>
      station.name.toLowerCase().includes(query.toLowerCase()) ||
      (station.state && station.state.toLowerCase().includes(query.toLowerCase()))
    );
  }
};

/**
 * Find nearby weather stations based on coordinates
 */
export const findNearbyStations = async (lat: number, lon: number, radiusKm: number = 50): Promise<WeatherStation[]> => {
  try {
    console.log(`Finding stations near ${lat}, ${lon} within ${radiusKm}km`);

    // Get all stations from NOAA API
    try {
      const data = await fetchNOAAData(
        `https://api.tidesandcurrents.noaa.gov/mdapi/prod/webapi/stations.json?type=tidepredictions`
      );
    const nearbyStations: WeatherStation[] = [];

    if (data?.stations) {
      for (const station of data.stations) {
        if (station.lat && station.lng && station.name && station.id) {
          const stationLat = parseFloat(station.lat);
          const stationLon = parseFloat(station.lng);
          const distance = calculateDistance(lat, lon, stationLat, stationLon);

          if (distance <= radiusKm) {
            nearbyStations.push({
              id: station.id,
              name: station.name,
              lat: stationLat,
              lon: stationLon,
              state: station.state || undefined,
              distance: Math.round(distance * 10) / 10 // Round to 1 decimal place
            });
          }
        }
      }
    }

      // Sort by distance and return closest stations
      nearbyStations.sort((a, b) => (a.distance || 0) - (b.distance || 0));
      return nearbyStations.slice(0, 15);
    } catch (apiError) {
      console.warn('NOAA station API not available, falling back to popular stations:', apiError);
      return calculateDistanceToPopularStations(lat, lon).slice(0, 10);
    }
  } catch (error) {
    console.error('Error finding nearby stations:', error);
    // Fallback to popular stations with distance calculation
    return calculateDistanceToPopularStations(lat, lon).slice(0, 10);
  }
};

/**
 * Calculate distance between two coordinates using Haversine formula
 */
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Calculate distances to popular stations and sort by proximity
 */
const calculateDistanceToPopularStations = (lat: number, lon: number): WeatherStation[] => {
  return POPULAR_STATIONS.map(station => ({
    ...station,
    distance: Math.round(calculateDistance(lat, lon, station.lat, station.lon) * 10) / 10
  })).sort((a, b) => (a.distance || 0) - (b.distance || 0));
};

/**
 * Validate and parse NOAA API response data
 */
const validateAndParseValue = (value: any, fieldName: string): number | null => {
  if (value === null || value === undefined || value === '') {
    return null;
  }

  const parsed = parseFloat(value);
  if (isNaN(parsed)) {
    console.warn(`Invalid ${fieldName} value: ${value}`);
    return null;
  }

  return parsed;
};

/**
 * Fetch data from NOAA API with error handling and retries
 * Falls back to mock data if CORS issues prevent API access
 */
const fetchNOAAData = async (url: string, retries: number = 2): Promise<any> => {
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, {
        mode: 'cors',
        // Remove User-Agent header as it can cause CORS issues in browsers
        headers: {
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Check for NOAA API error responses
      if (data.error) {
        throw new Error(`NOAA API Error: ${data.error.message || 'Unknown error'}`);
      }

      return data;
    } catch (error) {
      console.warn(`NOAA API attempt ${attempt + 1} failed:`, error);

      // Check if this is a CORS error
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn('CORS error detected, this is expected in browser environments');
        throw new Error('CORS_ERROR');
      }

      if (attempt === retries) {
        throw error;
      }

      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

/**
 * Generate mock weather data for testing when APIs are unavailable
 */
const generateMockWeatherData = (stationId: string): WeatherData => {
  const now = new Date();

  // Generate realistic but fake data based on station location
  const baseTemp = stationId.startsWith('94') ? 15 : 20; // Cooler for SF Bay area
  const tempVariation = Math.sin(now.getHours() / 24 * 2 * Math.PI) * 5;

  return {
    timestamp: now.toISOString(),
    temperature: baseTemp + tempVariation + (Math.random() - 0.5) * 2,
    waterTemperature: baseTemp - 2 + (Math.random() - 0.5) * 1,
    windSpeed: 5 + Math.random() * 15,
    windGust: 8 + Math.random() * 20,
    windDirection: Math.random() * 360,
    tideLevel: Math.sin(now.getTime() / (1000 * 60 * 60 * 6)) * 2, // 6-hour tide cycle
    lightning: false,
  };
};

/**
 * Get current weather data for a station using NOAA APIs
 */
export const getWeatherData = async (stationId: string): Promise<WeatherData> => {
  console.log(`Fetching weather data for station: ${stationId}`);

  try {
    // Initialize default values
    let temperature: number | null = null;
    let waterTemperature: number | null = null;
    let windSpeed: number | null = null;
    let windDirection: number | null = null;
    let windGust: number | null = null;
    let tideLevel: number | null = null;
    let timestamp = new Date().toISOString();
    let hasAnyData = false;
  
  try {
    // Get latest water level (tide)
    const tideData = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&datum=MLLW&units=metric&format=json&product=water_level`
    );

    if (tideData?.data?.[0]) {
      const value = validateAndParseValue(tideData.data[0].v, 'tide level');
      if (value !== null) {
        tideLevel = value;
        hasAnyData = true;
        // Use this timestamp as our reference since it's likely to be present
        if (tideData.data[0].t) {
          timestamp = new Date(tideData.data[0].t).toISOString();
        }
      }
    }
  } catch (error) {
    console.error('Error fetching tide data:', error);
  }
  
  try {
    // Get latest air temperature
    const airTempData = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=air_temperature`
    );

    if (airTempData?.data?.[0]) {
      const value = validateAndParseValue(airTempData.data[0].v, 'air temperature');
      if (value !== null) {
        temperature = value;
        hasAnyData = true;
      }
    }
  } catch (error) {
    console.error('Error fetching air temperature data:', error);
  }
  
  try {
    // Get latest water temperature
    const waterTempData = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=water_temperature`
    );

    if (waterTempData?.data?.[0]) {
      const value = validateAndParseValue(waterTempData.data[0].v, 'water temperature');
      if (value !== null) {
        waterTemperature = value;
        hasAnyData = true;
      }
    }
  } catch (error) {
    console.error('Error fetching water temperature data:', error);
  }

  try {
    // Get latest wind data
    const windData = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=wind`
    );

    if (windData?.data?.[0]) {
      const speedValue = validateAndParseValue(windData.data[0].s, 'wind speed');
      const directionValue = validateAndParseValue(windData.data[0].d, 'wind direction');
      const gustValue = validateAndParseValue(windData.data[0].g, 'wind gust');

      if (speedValue !== null) {
        windSpeed = speedValue;
        hasAnyData = true;
      }
      if (directionValue !== null) {
        windDirection = directionValue;
      }
      if (gustValue !== null) {
        windGust = gustValue;
      }
    }
  } catch (error) {
    console.error('Error fetching wind data:', error);
  }

  // If we have no data at all, check if it's due to CORS and use mock data
  if (!hasAnyData) {
    console.warn(`No weather data available for station ${stationId}, using mock data for development`);
    return generateMockWeatherData(stationId);
  }
  
    return {
      timestamp,
      temperature: temperature ?? 0,
      waterTemperature: waterTemperature ?? undefined,
      windSpeed: windSpeed ?? 0,
      windGust: windGust ?? 0,
      windDirection: windDirection ?? 0,
      tideLevel: tideLevel ?? undefined,
      lightning: false, // No lightning data from NOAA API
    };
  } catch (error) {
    console.error(`Error fetching weather data for station ${stationId}:`, error);

    // Check if it's a CORS error or any other API failure
    if (error instanceof Error && (error.message.includes('CORS') || error.message.includes('Failed to fetch'))) {
      console.warn('CORS error detected, using mock weather data for development');
    } else {
      console.warn('API error detected, using mock weather data as fallback');
    }

    return generateMockWeatherData(stationId);
  }
};

/**
 * Get station information including coordinates
 */
const getStationInfo = async (stationId: string): Promise<WeatherStation | null> => {
  try {
    // First check if it's in our popular stations list
    const popularStation = POPULAR_STATIONS.find(s => s.id === stationId);
    if (popularStation) {
      return popularStation;
    }

    // Otherwise, fetch from NOAA API
    const response = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/mdapi/prod/webapi/stations/${stationId}.json`
    );

    if (response?.stations?.[0]) {
      const station = response.stations[0];
      return {
        id: station.id,
        name: station.name,
        lat: parseFloat(station.lat),
        lon: parseFloat(station.lng),
        state: station.state
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching station info:', error);
    return null;
  }
};

/**
 * Get hourly forecast for a station using NOAA APIs
 */
export const getHourlyForecast = async (stationId: string): Promise<HourlyForecast> => {
  console.log(`Fetching hourly forecast for station: ${stationId}`);
  
  const hours: WeatherData[] = [];
  
  try {
    // Get station information for coordinates
    const stationInfo = await getStationInfo(stationId);

    // Get tide predictions for the next 24 hours
    const endDate = new Date();
    endDate.setHours(endDate.getHours() + 24);

    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date(endDate).toISOString().split('T')[0];

    const tideData = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&begin_date=${today}&end_date=${tomorrow}&time_zone=lst_ldt&datum=MLLW&interval=h&units=metric&format=json&product=predictions`
    );

    // Try to get actual weather forecast from NWS if we have coordinates
    let weatherForecast = null;
    if (stationInfo?.lat && stationInfo?.lon) {
      try {
        // Get NWS forecast for the station location
        const forecastResponse = await fetchNOAAData(
          `https://api.weather.gov/points/${stationInfo.lat},${stationInfo.lon}`
        );

        if (forecastResponse?.properties?.forecastHourly) {
          const hourlyForecastResponse = await fetchNOAAData(forecastResponse.properties.forecastHourly);
          weatherForecast = hourlyForecastResponse?.properties?.periods;
        }
      } catch (error) {
        console.warn('Could not fetch NWS weather forecast, using synthetic data:', error);
      }
    }
    
    // Get current weather to use as a base for forecast
    const currentWeather = await getWeatherData(stationId);
    
    // Generate hourly forecast combining tide data with weather forecast
    if (tideData?.predictions) {
      const now = new Date();

      // Process each prediction into an hourly forecast
      for (let i = 0; i < Math.min(24, tideData.predictions.length); i++) {
        const prediction = tideData.predictions[i];
        const forecastTime = new Date(prediction.t);

        // Only include hours that are in the future
        if (forecastTime >= now) {
          const hoursSinceNow = Math.floor((forecastTime.getTime() - now.getTime()) / (60 * 60 * 1000));

          // Try to find matching weather forecast data
          let weatherData = null;
          if (weatherForecast) {
            weatherData = weatherForecast.find((period: any) => {
              const periodStart = new Date(period.startTime);
              const periodEnd = new Date(period.endTime);
              return forecastTime >= periodStart && forecastTime < periodEnd;
            });
          }

          let temperature, windSpeed, windDirection, windGust;

          if (weatherData) {
            // Use actual forecast data
            temperature = weatherData.temperature || currentWeather.temperature;
            windSpeed = parseWindSpeed(weatherData.windSpeed) || currentWeather.windSpeed;
            windDirection = parseWindDirection(weatherData.windDirection) || currentWeather.windDirection;
            windGust = windSpeed * 1.3; // Estimate gust as 30% higher than sustained wind
          } else {
            // Fall back to synthetic data based on current weather
            const hourOfDay = forecastTime.getHours();

            // Temperature typically follows a diurnal cycle, peaking in afternoon
            const tempFactor = Math.sin(((hourOfDay - 6) % 24) / 24 * 2 * Math.PI);
            const tempVariation = tempFactor * 2; // +/- 2 degrees C variation

            // Wind often increases during the day and decreases at night
            const windFactor = Math.sin(((hourOfDay - 9) % 24) / 24 * 2 * Math.PI);
            const windVariation = windFactor * 1.5; // +/- 1.5 km/h variation

            // Wind direction often shifts throughout the day
            const directionVariation = hoursSinceNow * 5; // 5 degrees per hour

            temperature = currentWeather.temperature + tempVariation;
            windSpeed = Math.max(0, currentWeather.windSpeed + windVariation);
            windDirection = (currentWeather.windDirection + directionVariation) % 360;
            windGust = Math.max(0, currentWeather.windGust + windVariation * 1.5);
          }

          hours.push({
            timestamp: forecastTime.toISOString(),
            temperature,
            waterTemperature: currentWeather.waterTemperature,
            windSpeed,
            windGust,
            windDirection,
            tideLevel: validateAndParseValue(prediction.v, 'tide level'),
            lightning: false,
          });
        }
      }
    }
    
    return { hours };
  } catch (error) {
    console.error('Error fetching hourly forecast:', error);
    return { hours: [] };
  }
};

/**
 * Get weather alerts for a specific area using NOAA Weather Service API
 */
export const getWeatherAlerts = async (lat: number, lon: number): Promise<WeatherAlerts> => {
  try {
    console.log(`Fetching weather alerts for coordinates: ${lat}, ${lon}`);

    // Use NOAA Weather Service API for alerts
    const alertsData = await fetchNOAAData(
      `https://api.weather.gov/alerts/active?point=${lat},${lon}`
    );

    const alerts: WeatherAlert[] = [];

    if (alertsData?.features) {
      for (const feature of alertsData.features) {
        const properties = feature.properties;

        if (properties) {
          // Filter for marine and weather alerts relevant to boating
          const isRelevant = properties.event && (
            properties.event.toLowerCase().includes('marine') ||
            properties.event.toLowerCase().includes('wind') ||
            properties.event.toLowerCase().includes('storm') ||
            properties.event.toLowerCase().includes('weather') ||
            properties.event.toLowerCase().includes('fog') ||
            properties.event.toLowerCase().includes('gale') ||
            properties.event.toLowerCase().includes('advisory')
          );

          if (isRelevant) {
            alerts.push({
              id: properties.id || `alert-${Date.now()}-${Math.random()}`,
              title: properties.headline || properties.event || 'Weather Alert',
              description: properties.description || properties.instruction || 'No details available',
              severity: mapSeverity(properties.severity),
              urgency: mapUrgency(properties.urgency),
              certainty: mapCertainty(properties.certainty),
              event: properties.event || 'Weather Alert',
              effective: properties.effective || new Date().toISOString(),
              expires: properties.expires || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              areas: properties.areaDesc ? [properties.areaDesc] : []
            });
          }
        }
      }
    }

    return {
      alerts,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching weather alerts:', error);
    return {
      alerts: [],
      lastUpdated: new Date().toISOString()
    };
  }
};

/**
 * Map NOAA severity levels to our severity type
 */
const mapSeverity = (severity: string | undefined): WeatherAlert['severity'] => {
  if (!severity) return 'minor';

  const s = severity.toLowerCase();
  if (s.includes('extreme')) return 'extreme';
  if (s.includes('severe')) return 'severe';
  if (s.includes('moderate')) return 'moderate';
  return 'minor';
};

/**
 * Map NOAA urgency levels to our urgency type
 */
const mapUrgency = (urgency: string | undefined): WeatherAlert['urgency'] => {
  if (!urgency) return 'future';

  const u = urgency.toLowerCase();
  if (u.includes('immediate')) return 'immediate';
  if (u.includes('expected')) return 'expected';
  if (u.includes('past')) return 'past';
  return 'future';
};

/**
 * Map NOAA certainty levels to our certainty type
 */
const mapCertainty = (certainty: string | undefined): WeatherAlert['certainty'] => {
  if (!certainty) return 'possible';

  const c = certainty.toLowerCase();
  if (c.includes('observed')) return 'observed';
  if (c.includes('likely')) return 'likely';
  if (c.includes('unlikely')) return 'unlikely';
  return 'possible';
};

/**
 * Parse wind speed from NWS forecast text (e.g., "10 mph" -> 16.09 km/h)
 */
const parseWindSpeed = (windSpeedText: string): number | null => {
  if (!windSpeedText) return null;

  const match = windSpeedText.match(/(\d+)\s*mph/i);
  if (match) {
    const mph = parseInt(match[1]);
    return mph * 1.60934; // Convert mph to km/h
  }

  return null;
};

/**
 * Parse wind direction from NWS forecast text (e.g., "NW" -> 315 degrees)
 */
const parseWindDirection = (windDirectionText: string): number | null => {
  if (!windDirectionText) return null;

  const directions: { [key: string]: number } = {
    'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
    'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
    'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
    'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
  };

  const direction = windDirectionText.trim().toUpperCase();
  return directions[direction] || null;
};

/**
 * Detect what data types a station provides by testing API endpoints
 */
export const detectStationCapabilities = async (stationId: string): Promise<WeatherStationCapabilities> => {
  console.log(`Detecting capabilities for station: ${stationId}`);

  const capabilities: WeatherStationCapabilities = {
    airTemperature: false,
    waterTemperature: false,
    wind: false,
    waterLevel: false,
    predictions: false,
    barometricPressure: false,
    visibility: false
  };

  // Test each data type by making a request for recent data
  const testEndpoints = [
    {
      capability: 'airTemperature' as keyof WeatherStationCapabilities,
      product: 'air_temperature'
    },
    {
      capability: 'waterTemperature' as keyof WeatherStationCapabilities,
      product: 'water_temperature'
    },
    {
      capability: 'wind' as keyof WeatherStationCapabilities,
      product: 'wind'
    },
    {
      capability: 'waterLevel' as keyof WeatherStationCapabilities,
      product: 'water_level'
    },
    {
      capability: 'predictions' as keyof WeatherStationCapabilities,
      product: 'predictions'
    },
    {
      capability: 'barometricPressure' as keyof WeatherStationCapabilities,
      product: 'air_pressure'
    },
    {
      capability: 'visibility' as keyof WeatherStationCapabilities,
      product: 'visibility'
    }
  ];

  // Test each endpoint concurrently
  const tests = testEndpoints.map(async ({ capability, product }) => {
    try {
      let url;
      if (product === 'predictions') {
        // Predictions use a different endpoint format
        const today = new Date().toISOString().split('T')[0];
        url = `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&begin_date=${today}&end_date=${today}&time_zone=lst_ldt&datum=MLLW&units=metric&format=json&product=${product}`;
      } else {
        url = `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=${product}`;
      }

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'BoatBook/1.0 (Weather Station App)'
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Check if we got valid data (not an error response)
        if (product === 'predictions') {
          capabilities[capability] = !!(data?.predictions && data.predictions.length > 0);
        } else {
          capabilities[capability] = !!(data?.data && data.data.length > 0 && !data.error);
        }
      }
    } catch (error) {
      // Capability not available
      console.debug(`Station ${stationId} does not support ${capability}:`, error);
    }
  });

  // Wait for all tests to complete
  await Promise.allSettled(tests);

  console.log(`Station ${stationId} capabilities:`, capabilities);
  return capabilities;
};
