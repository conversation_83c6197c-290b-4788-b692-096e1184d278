
import { WeatherS<PERSON>, WeatherData, HourlyForecast } from '@/types/weather';

// Default station for San Francisco Bay
export const DEFAULT_STATION: WeatherStation = {
  id: '9414290',
  name: 'San Francisco, CA',
  lat: 37.8063,
  lon: -122.4659,
  state: 'CA'
};

// List of popular NOAA stations for quick access
export const POPULAR_STATIONS: WeatherStation[] = [
  { id: '9414290', name: 'San Francisco, CA', lat: 37.8063, lon: -122.4659, state: 'CA' },
  { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.2100, state: 'CA' },
  { id: '9414750', name: 'Alameda, CA', lat: 37.7717, lon: -122.3000, state: 'CA' },
  { id: '9414863', name: 'Richmond, CA', lat: 37.9300, lon: -122.4100, state: 'CA' },
  { id: '9415020', name: 'Point Reyes, CA', lat: 38.0050, lon: -122.9767, state: 'CA' },
  { id: '9413450', name: 'Monterey, CA', lat: 36.6050, lon: -121.8883, state: 'CA' },
  { id: '9410230', name: 'San Diego, CA', lat: 32.7142, lon: -117.1736, state: 'CA' },
  { id: '8418150', name: 'Boston, MA', lat: 42.3584, lon: -71.0498, state: 'MA' },
  { id: '8518750', name: 'The Battery, NY', lat: 40.7000, lon: -74.0150, state: 'NY' },
  { id: '8594900', name: 'Washington, DC', lat: 38.8744, lon: -77.0213, state: 'DC' },
  { id: '8638863', name: 'Sewells Point, VA', lat: 36.9467, lon: -76.3300, state: 'VA' },
  { id: '8665530', name: 'Charleston, SC', lat: 32.7806, lon: -79.9256, state: 'SC' },
  { id: '8723970', name: 'Miami Beach, FL', lat: 25.7686, lon: -80.1300, state: 'FL' },
  { id: '8771450', name: 'Galveston, TX', lat: 29.3100, lon: -94.7900, state: 'TX' },
  { id: '9431647', name: 'Neah Bay, WA', lat: 48.3683, lon: -124.6017, state: 'WA' },
  { id: '9447130', name: 'Seattle, WA', lat: 47.6026, lon: -122.3393, state: 'WA' },
];

/**
 * Search weather stations by name using NOAA stations list
 */
export const searchWeatherStations = async (query: string): Promise<WeatherStation[]> => {
  try {
    console.log(`Searching for weather stations with query: ${query}`);
    
    // Filter the popular stations based on the query
    return POPULAR_STATIONS.filter(station => 
      station.name.toLowerCase().includes(query.toLowerCase()) ||
      (station.state && station.state.toLowerCase().includes(query.toLowerCase()))
    );
  } catch (error) {
    console.error('Error searching weather stations:', error);
    return [];
  }
};

/**
 * Get current weather data for a station using NOAA APIs
 */
export const getWeatherData = async (stationId: string): Promise<WeatherData> => {
  console.log(`Fetching weather data for station: ${stationId}`);
  
  // Initialize default values
  let temperature = 0;
  let waterTemperature = null;
  let windSpeed = 0;
  let windDirection = 0;
  let windGust = 0; 
  let tideLevel = null;
  let timestamp = new Date().toISOString();
  
  try {
    // Get latest water level (tide)
    const tideResponse = await fetch(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&datum=MLLW&units=metric&format=json&product=water_level`
    );
    const tideData = await tideResponse.json();
    
    if (tideData?.data?.[0]?.v) {
      tideLevel = parseFloat(tideData.data[0].v);
      // Use this timestamp as our reference since it's likely to be present
      timestamp = new Date(tideData.data[0].t).toISOString();
    }
  } catch (error) {
    console.error('Error fetching tide data:', error);
  }
  
  try {
    // Get latest air temperature
    const airTempResponse = await fetch(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=air_temperature`
    );
    const airTempData = await airTempResponse.json();
    
    if (airTempData?.data?.[0]?.v) {
      temperature = parseFloat(airTempData.data[0].v);
    }
  } catch (error) {
    console.error('Error fetching air temperature data:', error);
  }
  
  try {
    // Get latest water temperature
    const waterTempResponse = await fetch(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=water_temperature`
    );
    const waterTempData = await waterTempResponse.json();
    
    if (waterTempData?.data?.[0]?.v) {
      waterTemperature = parseFloat(waterTempData.data[0].v);
    }
  } catch (error) {
    console.error('Error fetching water temperature data:', error);
  }
  
  try {
    // Get latest wind data
    const windResponse = await fetch(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=wind`
    );
    const windData = await windResponse.json();
    
    if (windData?.data?.[0]) {
      if (windData.data[0].s) windSpeed = parseFloat(windData.data[0].s);
      if (windData.data[0].d) windDirection = parseFloat(windData.data[0].d);
      if (windData.data[0].g) windGust = parseFloat(windData.data[0].g);
    }
  } catch (error) {
    console.error('Error fetching wind data:', error);
  }
  
  return {
    timestamp,
    temperature,
    waterTemperature,
    windSpeed,
    windGust,
    windDirection,
    tideLevel,
    lightning: false, // No lightning data from NOAA API
  };
};

/**
 * Get hourly forecast for a station using NOAA APIs
 */
export const getHourlyForecast = async (stationId: string): Promise<HourlyForecast> => {
  console.log(`Fetching hourly forecast for station: ${stationId}`);
  
  const hours: WeatherData[] = [];
  
  try {
    // Get tide predictions for the next 24 hours
    const endDate = new Date();
    endDate.setHours(endDate.getHours() + 24);
    
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date(endDate).toISOString().split('T')[0];
    
    const tidePredictions = await fetch(
      `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&begin_date=${today}&end_date=${tomorrow}&time_zone=lst_ldt&datum=MLLW&interval=h&units=metric&format=json&product=predictions`
    );
    const tideData = await tidePredictions.json();
    
    // Get current weather to use as a base for forecast
    const currentWeather = await getWeatherData(stationId);
    
    // Generate hourly forecast
    if (tideData?.predictions) {
      const now = new Date();
      
      // Process each prediction into an hourly forecast
      for (let i = 0; i < Math.min(24, tideData.predictions.length); i++) {
        const prediction = tideData.predictions[i];
        const forecastTime = new Date(prediction.t);
        
        // Only include hours that are in the future
        if (forecastTime >= now) {
          // We'll use the current weather as a baseline and adjust slightly for each hour
          // This is still an approximation but better than completely made-up data
          const hoursSinceNow = Math.floor((forecastTime.getTime() - now.getTime()) / (60 * 60 * 1000));
          const hourOfDay = forecastTime.getHours();
          
          // Temperature typically follows a diurnal cycle, peaking in afternoon
          const tempFactor = Math.sin(((hourOfDay - 6) % 24) / 24 * 2 * Math.PI);
          const tempVariation = tempFactor * 2; // +/- 2 degrees C variation
          
          // Wind often increases during the day and decreases at night
          const windFactor = Math.sin(((hourOfDay - 9) % 24) / 24 * 2 * Math.PI);
          const windVariation = windFactor * 1.5; // +/- 1.5 km/h variation
          
          // Wind direction often shifts throughout the day
          const directionVariation = hoursSinceNow * 5; // 5 degrees per hour
          
          hours.push({
            timestamp: forecastTime.toISOString(),
            temperature: currentWeather.temperature + tempVariation,
            waterTemperature: currentWeather.waterTemperature,
            windSpeed: Math.max(0, currentWeather.windSpeed + windVariation),
            windGust: Math.max(0, currentWeather.windGust + windVariation * 1.5),
            windDirection: (currentWeather.windDirection + directionVariation) % 360,
            tideLevel: parseFloat(prediction.v),
            lightning: false,
          });
        }
      }
    }
    
    return { hours };
  } catch (error) {
    console.error('Error fetching hourly forecast:', error);
    return { hours: [] };
  }
};
